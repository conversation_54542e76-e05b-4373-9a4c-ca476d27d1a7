"""
练习答案 - 完整实现版本
包含所有难度级别的参考答案
"""

import requests
import json
from datetime import datetime
import os
import time

# ============ 简单级别答案 ============

def get_weather_data_easy():
    """简单级别：获取天气数据"""
    print("开始获取天气数据...")
    
    city = "beijing"
    url = f"http://goweather.xyz/weather/{city}"
    
    response = requests.get(url)
    
    if response.status_code == 200:
        print("请求成功!")
        return response
    else:
        print("请求失败!")
        return None

def parse_weather_data_easy(response):
    """简单级别：解析天气数据"""
    if response is None:
        return None
    
    data = json.loads(response.text)
    
    print("\n当前天气信息:")
    print(f"温度: {data['temperature']}")
    print(f"风速: {data['wind']}")
    print(f"天气: {data['description']}")
    
    return data

def show_forecast_data_easy(data):
    """简单级别：显示预报数据"""
    if data is None:
        return
    
    print("\n未来天气预报:")
    
    forecast_list = data['forecast']
    
    first_day = forecast_list[0]
    print(f"第1天:")
    print(f"  温度: {first_day['temperature']}")
    print(f"  风速: {first_day['wind']}")
    
    if len(forecast_list) > 1:
        second_day = forecast_list[1]
        print(f"第2天:")
        print(f"  温度: {second_day['temperature']}")
        print(f"  风速: {second_day['wind']}")

def clothing_suggestion_easy(data):
    """简单级别：穿衣建议"""
    if data is None:
        return
    
    print("\n穿衣建议:")
    
    temp_str = data['temperature']
    temp_num = int(temp_str.replace(' °C', ''))
    
    wind_str = data['wind']
    wind_num = int(wind_str.replace(' km/h', ''))
    
    print(f"温度: {temp_num}°C, 风速: {wind_num}km/h")
    
    if temp_num < 5 and wind_num > 5:
        print("建议穿着：厚羽绒服、毛衣、保暖内衣、帽子、围巾、手套")
    elif temp_num > 15 and temp_num < 25:
        print("建议穿着：薄外套或针织衫、T恤、长裤")
    elif temp_num >= 25:
        print("建议穿着：T恤、短裤、防晒帽")
    else:
        print("建议穿着：长袖衬衫、薄外套、长裤")

# ============ 中等级别答案 ============

def get_weather_data_medium(city):
    """中等级别：获取指定城市天气数据"""
    print(f"正在获取{city}的天气数据...")
    
    try:
        url = f"http://goweather.xyz/weather/{city}"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            print(f"{city}天气数据获取成功!")
            return response
        else:
            print(f"{city}天气数据获取失败，状态码: {response.status_code}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"网络请求错误: {e}")
        return None

def parse_and_display_weather_medium(response, city_name):
    """中等级别：解析并显示天气信息"""
    if response is None:
        return None
    
    try:
        data = json.loads(response.text)
        
        print(f"\n{city_name}天气信息:")
        print(f"当前温度: {data.get('temperature', '未知')}")
        print(f"风速: {data.get('wind', '未知')}")
        print(f"天气状况: {data.get('description', '未知')}")
        
        if 'forecast' in data and data['forecast']:
            print(f"\n未来{len(data['forecast'])}天预报:")
            for i, forecast in enumerate(data['forecast'], 1):
                print(f"第{i}天: {forecast.get('temperature', '未知')} 风速{forecast.get('wind', '未知')}")
        
        return data
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        return None

def advanced_clothing_suggestion_medium(data):
    """中等级别：高级穿衣建议"""
    if data is None:
        return
    
    print("\n智能穿衣建议:")
    
    try:
        temp_str = data['temperature']
        temp_num = int(temp_str.replace(' °C', '').replace('+', ''))
        
        wind_str = data['wind']
        wind_num = int(wind_str.replace(' km/h', ''))
        
        description = data.get('description', '').lower()
        
        print(f"当前条件: {temp_num}°C, 风速{wind_num}km/h, {description}")
        
        # 基础建议
        if temp_num < 0:
            suggestion = "极寒装备：厚羽绒服、保暖内衣、雪地靴、帽子、围巾、手套"
        elif 0 <= temp_num < 5:
            suggestion = "厚重保暖：羽绒服、毛衣、保暖裤、靴子"
        elif 5 <= temp_num < 15:
            suggestion = "中等保暖：外套、长袖、长裤、运动鞋"
        elif 15 <= temp_num < 25:
            suggestion = "轻薄舒适：薄外套、T恤、长裤或短裤"
        else:
            suggestion = "夏季清凉：T恤、短裤、凉鞋、防晒帽"
        
        # 风速调整
        if wind_num > 10:
            suggestion += "，注意防风"
        
        # 天气状况调整
        if 'rain' in description or 'shower' in description:
            suggestion += "，携带雨具"
        elif 'snow' in description:
            suggestion += "，注意防滑"
        
        print(f"建议: {suggestion}")
        
    except (ValueError, KeyError) as e:
        print(f"数据处理错误: {e}")

def compare_cities_weather_medium():
    """中等级别：多城市天气对比"""
    cities = ["beijing", "shanghai", "guangzhou"]
    weather_data = {}
    
    print("\n多城市天气对比:")
    
    for city in cities:
        response = get_weather_data_medium(city)
        if response:
            try:
                data = json.loads(response.text)
                weather_data[city] = data
            except json.JSONDecodeError:
                continue
    
    if not weather_data:
        print("无法获取任何城市的天气数据")
        return
    
    # 显示对比
    print("\n城市天气对比表:")
    print(f"{'城市':<10} {'温度':<10} {'风速':<10} {'天气':<15}")
    print("-" * 50)
    
    temps = []
    for city, data in weather_data.items():
        temp_str = data.get('temperature', '0 °C')
        temp_num = int(temp_str.replace(' °C', '').replace('+', ''))
        temps.append((city, temp_num))
        
        city_name = {'beijing': '北京', 'shanghai': '上海', 'guangzhou': '广州'}.get(city, city)
        print(f"{city_name:<10} {data.get('temperature', '未知'):<10} {data.get('wind', '未知'):<10} {data.get('description', '未知'):<15}")
    
    # 找出极值
    if temps:
        hottest = max(temps, key=lambda x: x[1])
        coldest = min(temps, key=lambda x: x[1])
        
        print(f"\n温度最高: {hottest[0]} ({hottest[1]}°C)")
        print(f"温度最低: {coldest[0]} ({coldest[1]}°C)")
        
        if hottest[1] - coldest[1] > 10:
            print("建议选择温度适中的城市旅行")

# ============ 困难级别答案 ============

class WeatherAnalyzer:
    """完整的天气分析器实现"""
    
    def __init__(self):
        self.base_url = "http://goweather.xyz/weather/"
        self.supported_cities = {
            'beijing': '北京',
            'shanghai': '上海', 
            'guangzhou': '广州',
            'shenzhen': '深圳',
            'hangzhou': '杭州'
        }
        self.weather_cache = {}
        self.history_file = "weather_history.json"
        
    def get_weather(self, city):
        """获取天气数据"""
        try:
            url = f"{self.base_url}{city}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = json.loads(response.text)
                # 添加时间戳
                data['timestamp'] = datetime.now().isoformat()
                data['city'] = city
                return data
            else:
                print(f"获取{city}天气失败，状态码: {response.status_code}")
                return None
        except Exception as e:
            print(f"网络请求错误: {e}")
            return None
    
    def parse_weather_data(self, data):
        """解析天气数据"""
        if not data:
            return None
        
        try:
            parsed = {
                'city': data.get('city', '未知'),
                'timestamp': data.get('timestamp', ''),
                'current': {
                    'temperature': data.get('temperature', '未知'),
                    'wind': data.get('wind', '未知'),
                    'description': data.get('description', '未知')
                },
                'forecast': data.get('forecast', [])
            }
            
            # 提取数值
            temp_str = parsed['current']['temperature']
            if '°C' in temp_str:
                parsed['current']['temp_value'] = int(temp_str.replace(' °C', '').replace('+', ''))
            
            wind_str = parsed['current']['wind']
            if 'km/h' in wind_str:
                parsed['current']['wind_value'] = int(wind_str.replace(' km/h', ''))
            
            return parsed
        except Exception as e:
            print(f"数据解析错误: {e}")
            return None
    
    def clothing_advisor(self, weather_info):
        """智能穿衣建议"""
        if not weather_info:
            return "无法提供建议"
        
        try:
            temp = weather_info['current'].get('temp_value', 20)
            wind = weather_info['current'].get('wind_value', 0)
            desc = weather_info['current']['description'].lower()
            
            # 基础建议
            if temp < -10:
                base = "极寒防护：厚羽绒服、保暖内衣、雪地靴、帽子、围巾、手套"
            elif -10 <= temp < 0:
                base = "严寒保暖：羽绒服、毛衣、保暖裤、靴子、帽子"
            elif 0 <= temp < 5:
                base = "寒冷保暖：厚外套、毛衣、长裤、靴子"
            elif 5 <= temp < 15:
                base = "凉爽舒适：外套、长袖、长裤、运动鞋"
            elif 15 <= temp < 25:
                base = "温和宜人：薄外套、T恤、长裤或短裤"
            elif 25 <= temp < 30:
                base = "温暖舒适：T恤、短裤、运动鞋"
            else:
                base = "炎热防晒：轻薄T恤、短裤、凉鞋、防晒帽、太阳镜"
            
            # 风速调整
            if wind > 15:
                base += "，强风天气注意防风"
            elif wind > 10:
                base += "，注意防风"
            
            # 天气状况调整
            if any(word in desc for word in ['rain', 'shower', 'drizzle']):
                base += "，携带雨具"
            elif any(word in desc for word in ['snow', 'sleet']):
                base += "，注意防滑保暖"
            elif 'sunny' in desc and temp > 25:
                base += "，注意防晒"
            
            return base
        except Exception as e:
            return f"建议生成错误: {e}"
    
    def compare_cities(self, cities):
        """城市天气对比"""
        results = {}
        
        for city in cities:
            data = self.get_weather(city)
            if data:
                parsed = self.parse_weather_data(data)
                if parsed:
                    results[city] = parsed
        
        return results
    
    def save_data(self, city, weather_data):
        """保存数据到文件"""
        try:
            history = []
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
            
            history.append({
                'city': city,
                'data': weather_data,
                'saved_at': datetime.now().isoformat()
            })
            
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)
            
            print(f"数据已保存到 {self.history_file}")
        except Exception as e:
            print(f"保存数据失败: {e}")
    
    def load_history(self, city):
        """加载历史数据"""
        try:
            if not os.path.exists(self.history_file):
                return []
            
            with open(self.history_file, 'r', encoding='utf-8') as f:
                history = json.load(f)
            
            return [item for item in history if item['city'] == city]
        except Exception as e:
            print(f"加载历史数据失败: {e}")
            return []
    
    def weather_alert(self, weather_info):
        """天气预警"""
        alerts = []
        
        if not weather_info:
            return alerts
        
        try:
            temp = weather_info['current'].get('temp_value', 20)
            wind = weather_info['current'].get('wind_value', 0)
            desc = weather_info['current']['description'].lower()
            
            if temp < -15:
                alerts.append("极寒预警：温度过低，注意防寒保暖")
            elif temp > 35:
                alerts.append("高温预警：温度过高，注意防暑降温")
            
            if wind > 20:
                alerts.append("大风预警：风力较强，注意出行安全")
            
            if any(word in desc for word in ['storm', 'thunder']):
                alerts.append("雷暴预警：恶劣天气，建议减少外出")
            
            return alerts
        except Exception as e:
            return [f"预警系统错误: {e}"]
    
    def display_weather_report(self, city, weather_info):
        """显示天气报告"""
        if not weather_info:
            print(f"无法显示{city}的天气报告")
            return
        
        city_name = self.supported_cities.get(city, city)
        print(f"\n{'='*50}")
        print(f"{city_name}天气报告")
        print(f"{'='*50}")
        
        current = weather_info['current']
        print(f"当前天气: {current['description']}")
        print(f"温度: {current['temperature']}")
        print(f"风速: {current['wind']}")
        
        if weather_info['forecast']:
            print(f"\n未来{len(weather_info['forecast'])}天预报:")
            for i, forecast in enumerate(weather_info['forecast'], 1):
                print(f"第{i}天: {forecast.get('temperature', '未知')} 风速{forecast.get('wind', '未知')}")
        
        # 穿衣建议
        suggestion = self.clothing_advisor(weather_info)
        print(f"\n穿衣建议: {suggestion}")
        
        # 天气预警
        alerts = self.weather_alert(weather_info)
        if alerts:
            print(f"\n天气预警:")
            for alert in alerts:
                print(f"⚠️  {alert}")
    
    def interactive_menu(self):
        """交互菜单"""
        while True:
            print(f"\n{'='*40}")
            print("天气查询系统")
            print(f"{'='*40}")
            print("1. 查询单个城市天气")
            print("2. 多城市天气对比")
            print("3. 查看历史数据")
            print("4. 退出程序")
            
            choice = input("\n请选择功能 (1-4): ").strip()
            
            if choice == '1':
                self.single_city_query()
            elif choice == '2':
                self.multi_city_compare()
            elif choice == '3':
                self.view_history()
            elif choice == '4':
                print("感谢使用天气查询系统！")
                break
            else:
                print("无效选择，请重新输入")
    
    def single_city_query(self):
        """单城市查询"""
        print("\n支持的城市:")
        for code, name in self.supported_cities.items():
            print(f"{code} - {name}")
        
        city = input("\n请输入城市代码: ").strip().lower()
        
        if city not in self.supported_cities:
            print("不支持的城市")
            return
        
        data = self.get_weather(city)
        if data:
            weather_info = self.parse_weather_data(data)
            self.display_weather_report(city, weather_info)
            
            # 询问是否保存
            save = input("\n是否保存数据？(y/n): ").strip().lower()
            if save == 'y':
                self.save_data(city, data)
    
    def multi_city_compare(self):
        """多城市对比"""
        cities = list(self.supported_cities.keys())[:3]  # 取前3个城市
        results = self.compare_cities(cities)
        
        if not results:
            print("无法获取城市天气数据")
            return
        
        print(f"\n{'='*60}")
        print("多城市天气对比")
        print(f"{'='*60}")
        print(f"{'城市':<10} {'温度':<12} {'风速':<12} {'天气':<15}")
        print("-" * 60)
        
        for city, info in results.items():
            city_name = self.supported_cities.get(city, city)
            current = info['current']
            print(f"{city_name:<10} {current['temperature']:<12} {current['wind']:<12} {current['description']:<15}")
    
    def view_history(self):
        """查看历史数据"""
        city = input("请输入要查看历史的城市代码: ").strip().lower()
        history = self.load_history(city)
        
        if not history:
            print(f"没有找到{city}的历史数据")
            return
        
        print(f"\n{self.supported_cities.get(city, city)}历史天气数据:")
        for i, record in enumerate(history[-5:], 1):  # 显示最近5条
            data = record['data']
            saved_time = record['saved_at'][:19].replace('T', ' ')
            print(f"{i}. {saved_time} - {data.get('temperature', '未知')} {data.get('description', '未知')}")
    
    def run(self):
        """主运行方法"""
        print("欢迎使用天气查询分析系统！")
        self.interactive_menu()

def main():
    """主程序入口"""
    analyzer = WeatherAnalyzer()
    analyzer.run()

if __name__ == "__main__":
    main()

# ============ 简单级别完整示例 ============

def run_easy_example():
    """运行简单级别完整示例"""
    print("=" * 50)
    print("天气查询练习 - 简单级别")
    print("=" * 50)

    response = get_weather_data_easy()
    if response:
        print(f"\n服务器返回的原始数据:")
        print(response.text)

        data = parse_weather_data_easy(response)
        show_forecast_data_easy(data)
        clothing_suggestion_easy(data)

    print("\n练习完成！")

def run_medium_example():
    """运行中等级别完整示例"""
    print("=" * 50)
    print("天气查询练习 - 中等级别")
    print("=" * 50)

    city = "beijing"
    response = get_weather_data_medium(city)
    data = parse_and_display_weather_medium(response, "北京")
    advanced_clothing_suggestion_medium(data)
    compare_cities_weather_medium()

    print("\n练习完成！")
